<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小学英语单词学习系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .word-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background-color: #f9f9f9;
            text-align: center;
            min-width: 120px;
        }
        .word-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        .grid-item {
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 80px;
            background-color: #f0f0f0;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .user-panel, .word-learning, .memory-grid, .admin-panel {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        input {
            padding: 8px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>小学英语单词学习系统</h1>
    <div id="app">
        <div class="user-panel">
            <h2>用户: <span id="username">未登录</span></h2>
            <div class="login-form">
                <input type="text" id="login-input" placeholder="输入用户名">
                <button onclick="login()">登录</button>
            </div>
        </div>
        
        <div class="word-learning">
            <h3>当前学习组</h3>
            <div class="word-cards" id="word-cards">
                <!-- 单词卡片将在这里动态生成 -->
            </div>
            <div class="controls">
                <button onclick="nextGroup()">下一组</button>
                <button onclick="review()">复习</button>
            </div>
        </div>
        
        <div class="memory-grid">
            <h3>九宫格记忆系统</h3>
            <div class="grid-container" id="memory-grid">
                <!-- 九宫格将在这里动态生成 -->
            </div>
        </div>
        
        <div class="admin-panel" id="admin-panel" style="display:none;">
            <h3>管理员面板</h3>
            <div id="all-progress">
                <!-- 所有学生进度将在这里显示 -->
            </div>
        </div>
    </div>
    <script src="app.js"></script>
</body>
</html>