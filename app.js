// 自然拼读规则
const phonicsRules = {
    "CVC": /^[bcdfghjklmnpqrstvwxyz][aeiou][bcdfghjklmnpqrstvwxyz]$/i,
    "CVCE": /^[bcdfghjklmnpqrstvwxyz][aeiou][bcdfghjklmnpqrstvwxyz]e$/i,
    "VowelTeams": /[aeiou]{2,}/i
};

// 从小学考纲单词.md加载单词数据
const words = [
    {word: "a", translation: "一个"},
    {word: "about", translation: "大约"},
    {word: "after", translation: "之后"},
    {word: "afternoon", translation: "下午"},
    {word: "again", translation: "再一次"},
    {word: "age", translation: "年龄"},
    {word: "ago", translation: "以前"},
    {word: "air", translation: "空气"},
    {word: "all", translation: "全部的"},
    // 更多单词...
];

// 分组学习逻辑
function groupWords(words, groupSize=5, chapterSize=3) {
    const groups = [];
    for (let i = 0; i < words.length; i += groupSize) {
        groups.push(words.slice(i, i + groupSize));
    }
    
    const chapters = [];
    for (let i = 0; i < groups.length; i += chapterSize) {
        chapters.push(groups.slice(i, i + chapterSize));
    }
    
    return chapters;
}

// 九宫格记忆系统
class MemoryGrid {
    constructor() {
        this.grids = Array(9).fill().map(() => []);
        this.reviewIntervals = [1, 2, 4, 7, 15, 30, 60, 90, 120]; // 艾宾豪斯记忆曲线间隔(天)
    }
    
    addWord(word, gridLevel) {
        if (gridLevel >= 0 && gridLevel < 9) {
            this.grids[gridLevel].push(word);
        }
    }
    
    promoteWord(word) {
        for (let i = 0; i < this.grids.length; i++) {
            const index = this.grids[i].indexOf(word);
            if (index !== -1) {
                this.grids[i].splice(index, 1);
                if (i < 8) {
                    this.grids[i+1].push(word);
                }
                return true;
            }
        }
        return false;
    }
}

// 用户管理系统
class UserManager {
    constructor() {
        this.users = {};
        this.currentUser = null;
        this.adminUsers = ['admin'];
    }
    
    login(username, isAdmin=false) {
        if (!this.users[username]) {
            this.users[username] = {
                progress: {
                    currentGroup: 0,
                    currentChapter: 0,
                    masteredWords: []
                },
                memoryGrid: new MemoryGrid(),
                isAdmin: isAdmin || this.adminUsers.includes(username)
            };
        }
        this.currentUser = username;
        return this.users[username];
    }
    
    getProgress(username) {
        return this.users[username]?.progress || null;
    }
    
    getAllProgress() {
        return Object.entries(this.users).map(([name, data]) => ({
            name,
            progress: data.progress
        }));
    }
}

// 初始化应用
function initApp() {
    window.userManager = new UserManager();
    window.wordChapters = groupWords(words);
    
    // 渲染UI
    renderUI(userManager.users[userManager.currentUser], wordChapters);
}

// 登录函数
function login() {
    const username = document.getElementById('login-input').value.trim();
    if (username) {
        userManager.login(username);
        renderUI(userManager.users[username], wordChapters);
    }
}

// 下一组单词
function nextGroup() {
    const user = userManager.users[userManager.currentUser];
    const currentChapter = wordChapters[user.progress.currentChapter];
    
    if (user.progress.currentGroup < currentChapter.groups.length - 1) {
        user.progress.currentGroup++;
    } else if (user.progress.currentChapter < wordChapters.length - 1) {
        user.progress.currentChapter++;
        user.progress.currentGroup = 0;
    }
    
    renderUI(user, wordChapters);
}

// 复习单词
function review() {
    const user = userManager.users[userManager.currentUser];
    user.memoryGrid.promoteWord();
    renderUI(user, wordChapters);
}

function renderUI(user, chapters) {
    // 更新用户名显示
    document.getElementById('username').textContent = userManager.currentUser;
    
    // 如果是管理员，显示管理员面板
    if (user.isAdmin) {
        document.getElementById('admin-panel').style.display = 'block';
        renderAllProgress();
    }
    
    // 渲染当前学习组的单词卡片
    renderWordCards(user, chapters);
    
    // 渲染九宫格
    renderMemoryGrid(user);
}

function renderWordCards(user, chapters) {
    const wordCardsContainer = document.getElementById('word-cards');
    wordCardsContainer.innerHTML = '';
    
    const currentChapter = chapters[user.progress.currentChapter];
    const currentGroup = currentChapter.groups[user.progress.currentGroup];
    
    currentGroup.words.forEach(word => {
        const card = document.createElement('div');
        card.className = 'word-card';
        card.innerHTML = `
            <div class="word">${word.word}</div>
            <div class="pronunciation">${word.pronunciation}</div>
            <div class="meaning">${word.meaning}</div>
        `;
        wordCardsContainer.appendChild(card);
    });
}

function renderMemoryGrid(user) {
    const gridContainer = document.getElementById('memory-grid');
    gridContainer.innerHTML = '';
    
    // 创建9个格子
    for (let i = 0; i < 9; i++) {
        const gridItem = document.createElement('div');
        gridItem.className = 'grid-item';
        gridItem.textContent = user.memoryGrid.grid[i]?.word || '';
        gridContainer.appendChild(gridItem);
    }
}

function renderAllProgress() {
    const progressContainer = document.getElementById('all-progress');
    const allProgress = userManager.getAllProgress();
    
    progressContainer.innerHTML = allProgress.map(user => 
        `<div>${user.name}: 第${user.progress.currentChapter + 1}章 第${user.progress.currentGroup + 1}组</div>`
    ).join('');
}

initApp();