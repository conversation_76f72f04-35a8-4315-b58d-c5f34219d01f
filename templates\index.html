<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词学习系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .word-card {
            border: 2px solid #ddd;
            padding: 15px;
            margin: 8px;
            border-radius: 10px;
            background-color: white;
            text-align: center;
            min-width: 120px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .word-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .word-card.mastered {
            background-color: #e8f5e8;
            border-color: #4CAF50;
        }
        .word-card.selected {
            background-color: #e3f2fd;
            border-color: #2196F3;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        .word-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        /* 九宫格样式 */
        .nine-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .grid-cell {
            background: white;
            border: 3px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        .grid-cell:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }
        .grid-cell.completed {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-color: #4CAF50;
        }
        .grid-cell.active {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-color: #2196F3;
        }
        .grid-progress {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .grid-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 12px 24px;
            margin: 0 8px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .review-btn {
            background-color: #2196F3;
        }
        .review-btn:hover {
            background-color: #1976D2;
        }
        .logout-btn {
            background-color: #f44336;
        }
        .logout-btn:hover {
            background-color: #d32f2f;
        }
        .user-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .tab-container {
            display: flex;
            margin-bottom: 30px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: white;
            transition: all 0.3s;
            flex: 1;
            text-align: center;
        }
        .tab:hover {
            background-color: #f5f5f5;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .content-panel {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .nine-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .user-panel {
                flex-direction: column;
                gap: 15px;
            }
            .tab-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="user-panel">
        <div>
            <h1>英语单词学习系统</h1>
            <p>欢迎回来，{{ username }}！</p>
        </div>
        <div>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>

    <!-- 学习统计面板 -->
    <div class="stats-panel">
        <div class="stat-card">
            <div class="stat-number" id="mastered-count">0</div>
            <div class="stat-label">已掌握单词</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="current-chapter">0</div>
            <div class="stat-label">当前章节</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="study-days">0</div>
            <div class="stat-label">学习天数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-progress">0%</div>
            <div class="stat-label">总体进度</div>
        </div>
    </div>

    <div class="tab-container">
        <div class="tab active" onclick="switchTab('nine-grid')">九宫格学习</div>
        <div class="tab" onclick="switchTab('learn')">单词卡片</div>
        <div class="tab" onclick="switchTab('review')">复习单词</div>
        {% if user.isAdmin %}
            <div class="tab" onclick="switchTab('admin')">学生管理</div>
        {% endif %}
    </div>

    <!-- 九宫格学习界面 -->
    <div id="nine-grid-tab" class="content-panel">
        <div class="controls">
            <button onclick="prevChapter()" id="prev-chapter-btn">上一章</button>
            <span id="chapter-info">第 1 章</span>
            <button onclick="nextChapter()" id="next-chapter-btn">下一章</button>
        </div>
        <div class="nine-grid" id="nine-grid"></div>
        <div class="controls">
            <button class="review-btn" onclick="startChapterReview()">开始章节复习</button>
        </div>
    </div>

    <!-- 单词卡片学习界面 -->
    <div id="learn-tab" class="content-panel" style="display:none;">
        <div class="word-learning">
            <h3>当前学习组 - 第 <span id="current-chapter-display">1</span> 章 第 <span id="current-group-display">1</span> 组</h3>
            <div class="word-cards" id="word-cards"></div>
            <div class="controls">
                <button onclick="prevGroup()">上一组</button>
                <button onclick="nextGroup()">下一组</button>
                <button class="review-btn" onclick="markAsMastered()">标记为已掌握</button>
            </div>
        </div>
    </div>

    <!-- 复习界面 -->
    <div id="review-tab" class="content-panel" style="display:none;">
        <div class="review-panel">
            <h3>需要复习的单词</h3>
            <div class="word-cards" id="review-cards"></div>
            <div class="controls">
                <button class="review-btn" onclick="loadReviewWords()">刷新复习列表</button>
                <button onclick="markReviewCompleted()">完成复习</button>
            </div>
        </div>
    </div>

    <!-- 管理员界面 -->
    {% if user.isAdmin %}
    <div id="admin-tab" class="content-panel" style="display:none;">
        <div class="admin-panel">
            <h3>学生学习进度管理</h3>
            <div class="controls">
                <button class="review-btn" onclick="loadStudentProgress()">刷新数据</button>
            </div>
            <div id="student-progress">
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background-color: #f5f5f5;">
                            <th style="padding: 12px; border: 1px solid #ddd;">学生姓名</th>
                            <th style="padding: 12px; border: 1px solid #ddd;">当前章节</th>
                            <th style="padding: 12px; border: 1px solid #ddd;">当前组</th>
                            <th style="padding: 12px; border: 1px solid #ddd;">已掌握单词</th>
                            <th style="padding: 12px; border: 1px solid #ddd;">学习天数</th>
                            <th style="padding: 12px; border: 1px solid #ddd;">最后学习</th>
                        </tr>
                    </thead>
                    <tbody id="students-table-body">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <script>
        let currentChapter = {{ user.progress.currentChapter }};
        let currentGroup = {{ user.progress.currentGroup }};
        let masteredWords = new Set({{ user.progress.masteredWords|tojson }});
        let totalChapters = {{ total_chapters }};
        let totalWords = {{ total_words }};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            loadNineGrid();
            loadWords();

            // 如果是管理员，加载学生进度
            {% if user.isAdmin %}
            loadStudentProgress();
            {% endif %}
        });

        // 更新统计信息
        function updateStats() {
            document.getElementById('mastered-count').textContent = masteredWords.size;
            document.getElementById('current-chapter').textContent = currentChapter + 1;
            document.getElementById('study-days').textContent = {{ user.stats.studyDays or 0 }};

            const progress = Math.round((masteredWords.size / totalWords) * 100);
            document.getElementById('total-progress').textContent = progress + '%';
        }

        // 加载九宫格
        async function loadNineGrid() {
            try {
                const response = await fetch('/api/nine-grid');
                const data = await response.json();

                const container = document.getElementById('nine-grid');
                const chapterInfo = document.getElementById('chapter-info');

                chapterInfo.textContent = `第 ${data.chapter + 1} 章`;

                container.innerHTML = data.grid.map((group, index) => {
                    const completedClass = group.isCompleted ? 'completed' : '';
                    const activeClass = (currentGroup === index) ? 'active' : '';

                    return `
                        <div class="grid-cell ${completedClass} ${activeClass}"
                             onclick="selectGroup(${index})"
                             data-group="${index}">
                            <div class="grid-title">第 ${index + 1} 组</div>
                            <div class="grid-progress">${group.masteredCount}/${group.totalCount} 已掌握</div>
                            ${group.isCompleted ? '<div class="badge">✓</div>' : ''}
                        </div>
                    `;
                }).join('');

                // 更新按钮状态
                document.getElementById('prev-chapter-btn').disabled = (currentChapter === 0);
                document.getElementById('next-chapter-btn').disabled = (currentChapter >= totalChapters - 1);

            } catch (error) {
                console.error('加载九宫格失败:', error);
            }
        }

        // 选择学习组
        function selectGroup(groupIndex) {
            currentGroup = groupIndex;
            updateProgress();
            loadWords();
            switchTab('learn');
        }

        async function loadWords() {
            try {
                const response = await fetch('/api/words');
                const data = await response.json();

                const container = document.getElementById('word-cards');
                document.getElementById('current-chapter-display').textContent = data.chapter + 1;
                document.getElementById('current-group-display').textContent = data.group + 1;

                container.innerHTML = data.words.map(word => {
                    const masteredClass = masteredWords.has(word.word) ? 'mastered' : '';
                    return `
                        <div class="word-card ${masteredClass}" data-word="${word.word}">
                            <div style="font-size: 18px; font-weight: bold;">${word.word}</div>
                            <div style="color: #666; margin: 5px 0;">${word.pronunciation}</div>
                            <div style="color: #333;">${word.meaning}</div>
                            ${masteredWords.has(word.word) ? '<div class="badge">✓</div>' : ''}
                        </div>
                    `;
                }).join('');
            } catch (error) {
                console.error('加载单词失败:', error);
            }
        }

        // 章节导航
        async function prevChapter() {
            if (currentChapter > 0) {
                currentChapter--;
                await updateProgress();
                loadNineGrid();
            }
        }

        async function nextChapter() {
            if (currentChapter < totalChapters - 1) {
                currentChapter++;
                await updateProgress();
                loadNineGrid();
            }
        }

        // 组导航
        async function prevGroup() {
            if (currentGroup > 0) {
                currentGroup--;
            } else if (currentChapter > 0) {
                currentChapter--;
                currentGroup = 2; // 每章3组，索引0-2
            }
            await updateProgress();
            loadWords();
            loadNineGrid();
        }

        async function nextGroup() {
            if (currentGroup < 2) {
                currentGroup++;
            } else if (currentChapter < totalChapters - 1) {
                currentChapter++;
                currentGroup = 0;
            }
            await updateProgress();
            loadWords();
            loadNineGrid();
        }

        async function markAsMastered() {
            const selectedWords = Array.from(document.querySelectorAll('.word-card.selected'))
                .map(card => card.dataset.word);

            if (selectedWords.length === 0) {
                alert('请先选择要标记的单词');
                return;
            }

            try {
                await fetch('/api/progress', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        masteredWords: selectedWords
                    })
                });

                selectedWords.forEach(word => masteredWords.add(word));
                updateStats();
                loadWords();
                loadNineGrid();

                // 清除选择状态
                document.querySelectorAll('.word-card.selected').forEach(card => {
                    card.classList.remove('selected');
                });

            } catch (error) {
                console.error('标记单词失败:', error);
                alert('标记失败，请重试');
            }
        }

        // 更新进度
        async function updateProgress() {
            try {
                await fetch('/api/progress', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        currentChapter: currentChapter,
                        currentGroup: currentGroup
                    })
                });
            } catch (error) {
                console.error('更新进度失败:', error);
            }
        }

        async function loadReviewWords() {
            try {
                const response = await fetch('/api/review');
                const data = await response.json();

                const container = document.getElementById('review-cards');
                if (data.words.length === 0) {
                    container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无需要复习的单词</div>';
                } else {
                    container.innerHTML = data.words.map(word => {
                        return `
                            <div class="word-card" data-word="${word.word}">
                                <div style="font-size: 18px; font-weight: bold;">${word.word}</div>
                                <div style="color: #666; margin: 5px 0;">复习阶段: ${word.stage + 1}/5</div>
                                <div style="color: #333; font-size: 12px;">
                                    ${word.nextReview ? '下次复习: ' + new Date(word.nextReview).toLocaleString() : '复习完成'}
                                </div>
                            </div>
                        `;
                    }).join('');
                }
            } catch (error) {
                console.error('加载复习单词失败:', error);
            }
        }

        async function markReviewCompleted() {
            const reviewWords = Array.from(document.querySelectorAll('#review-cards .word-card'))
                .map(card => card.dataset.word);

            if (reviewWords.length === 0) {
                alert('没有需要复习的单词');
                return;
            }

            // 这里可以添加复习完成的逻辑
            alert('复习完成！');
            loadReviewWords();
        }

        async function startChapterReview() {
            // 获取当前章节的所有已掌握单词进行复习
            switchTab('review');
            loadReviewWords();
        }

        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 找到对应的标签并激活
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                if (tab.textContent.includes('九宫格') && tabName === 'nine-grid') {
                    tab.classList.add('active');
                } else if (tab.textContent.includes('单词卡片') && tabName === 'learn') {
                    tab.classList.add('active');
                } else if (tab.textContent.includes('复习') && tabName === 'review') {
                    tab.classList.add('active');
                } else if (tab.textContent.includes('管理') && tabName === 'admin') {
                    tab.classList.add('active');
                }
            });

            // 隐藏所有内容面板
            document.getElementById('nine-grid-tab').style.display = 'none';
            document.getElementById('learn-tab').style.display = 'none';
            document.getElementById('review-tab').style.display = 'none';
            if (document.getElementById('admin-tab')) {
                document.getElementById('admin-tab').style.display = 'none';
            }

            // 显示选中的面板
            document.getElementById(`${tabName}-tab`).style.display = 'block';

            // 加载对应内容
            if (tabName === 'review') {
                loadReviewWords();
            } else if (tabName === 'nine-grid') {
                loadNineGrid();
            } else if (tabName === 'admin') {
                loadStudentProgress();
            }
        }

        async function loadStudentProgress() {
            try {
                const response = await fetch('/api/admin/students');
                const data = await response.json();

                const tbody = document.getElementById('students-table-body');
                if (data.students.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px;">暂无学生数据</td></tr>';
                } else {
                    tbody.innerHTML = data.students.map(student => {
                        return `
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd;">${student.username}</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">第 ${student.currentChapter + 1} 章</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">第 ${student.currentGroup + 1} 组</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">${student.masteredWordsCount} 个</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">${student.studyDays} 天</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">${student.lastStudyDate}</td>
                            </tr>
                        `;
                    }).join('');
                }
            } catch (error) {
                console.error('加载学生进度失败:', error);
            }
        }

        async function logout() {
            try {
                await fetch('/logout');
                window.location.href = '/login';
            } catch (error) {
                window.location.href = '/login';
            }
        }

        // 事件监听器
        document.addEventListener('click', function(e) {
            // 单词卡片点击选择
            if (e.target.closest('.word-card') && !e.target.closest('.badge') && !e.target.closest('#review-cards')) {
                const card = e.target.closest('.word-card');
                card.classList.toggle('selected');
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        switchTab('nine-grid');
                        break;
                    case '2':
                        e.preventDefault();
                        switchTab('learn');
                        break;
                    case '3':
                        e.preventDefault();
                        switchTab('review');
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        prevGroup();
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        nextGroup();
                        break;
                }
            }
        });
    </script>
</body>
</html>