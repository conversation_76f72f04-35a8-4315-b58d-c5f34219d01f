<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词学习系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .word-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
            min-width: 120px;
            position: relative;
        }
        .word-card.mastered {
            background-color: #e6f7e6;
            border-color: #4CAF50;
        }
        .word-card.selected {
            background-color: #e3f2fd;
            border-color: #2196F3;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }
        .word-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .review-btn {
            background-color: #2196F3;
        }
        .logout-btn {
            background-color: #f44336;
        }
        .user-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tab-container {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid #ddd;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="user-panel">
        <h1>英语单词学习系统</h1>
        <div>
            <span>欢迎, {{ user.username }}</span>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>

    <div class="tab-container">
        <div class="tab active" onclick="switchTab('learn')">学习新词</div>
        <div class="tab" onclick="switchTab('review')">复习单词</div>
        {% if user.isAdmin %}
            <div class="tab" onclick="switchTab('admin')">管理</div>
        {% endif %}
    </div>

    <div id="learn-tab">
        <div class="word-learning">
            <h3>当前学习组</h3>
            <div class="word-cards" id="word-cards"></div>
            <div class="controls">
                <button onclick="nextGroup()">下一组</button>
                <button class="review-btn" onclick="markAsMastered()">标记为已掌握</button>
            </div>
        </div>
    </div>

    <div id="review-tab" style="display:none;">
        <div class="review-panel">
            <h3>需要复习的单词</h3>
            <div class="word-cards" id="review-cards"></div>
            <div class="controls">
                <button class="review-btn" onclick="loadReviewWords()">刷新</button>
            </div>
        </div>
    </div>

    {% if user.isAdmin %}
    <div id="admin-tab" style="display:none;">
        <div class="admin-panel">
            <h3>学生进度</h3>
            <div id="student-progress"></div>
        </div>
    </div>
    {% endif %}

    <script>
        let currentChapter = {{ user.progress.currentChapter }};
        let currentGroup = {{ user.progress.currentGroup }};
        let masteredWords = new Set({{ user.progress.masteredWords|tojson }});

        // 加载初始单词
        loadWords();
        
        // 如果是管理员，加载学生进度
        {% if user.isAdmin %}
        loadStudentProgress();
        {% endif %}

        async function loadWords() {
            const response = await fetch('/api/words');
            const data = await response.json();
            
            const container = document.getElementById('word-cards');
            container.innerHTML = data.words.map(word => {
                const masteredClass = masteredWords.has(word.word) ? 'mastered' : '';
                return `
                    <div class="word-card ${masteredClass}" data-word="${word.word}">
                        ${word.word}
                        <div>${word.pronunciation}</div>
                        <div>${word.meaning}</div>
                        ${masteredWords.has(word.word) ? '<div class="badge">✓</div>' : ''}
                    </div>
                `;
            }).join('');
        }

        async function markAsMastered() {
            const selectedWords = Array.from(document.querySelectorAll('.word-card')).filter(
                card => card.classList.contains('selected')
            ).map(card => card.dataset.word);
            
            if (selectedWords.length === 0) {
                alert('请先选择单词');
                return;
            }
            
            await fetch('/api/progress', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    masteredWords: selectedWords
                })
            });
            
            selectedWords.forEach(word => masteredWords.add(word));
            loadWords();
        }

        async function loadReviewWords() {
            const response = await fetch('/api/review');
            const data = await response.json();
            
            const container = document.getElementById('review-cards');
            container.innerHTML = data.words.map(word => {
                return `
                    <div class="word-card">
                        ${word.word}
                        <div>复习阶段: ${word.stage + 1}/5</div>
                        <div>下次复习: ${new Date(word.nextReview).toLocaleString()}</div>
                    </div>
                `;
            }).join('');
        }

        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
            
            document.getElementById('learn-tab').style.display = 'none';
            document.getElementById('review-tab').style.display = 'none';
            if (document.getElementById('admin-tab')) {
                document.getElementById('admin-tab').style.display = 'none';
            }
            
            document.getElementById(`${tabName}-tab`).style.display = 'block';
            
            if (tabName === 'review') {
                loadReviewWords();
            }
        }

        async function logout() {
            await fetch('/logout');
            window.location.href = '/login';
        }

        // 单词卡片点击选择
        document.addEventListener('click', function(e) {
            if (e.target.closest('.word-card') && !e.target.closest('.badge')) {
                const card = e.target.closest('.word-card');
                card.classList.toggle('selected');
            }
        });
    </script>
</body>
</html>