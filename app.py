from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import json
import os
from datetime import datetime, timedelta
import hashlib

app = Flask(__name__)
app.secret_key = 'your_secret_key_here_change_in_production'

# 数据文件路径
USERS_DATA_FILE = 'users_data.json'

# 加载单词数据
def load_words():
    words = []
    try:
        with open('小学考纲单词.md', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[2:]:  # 跳过前两行标题
                if line.strip() and '\t' in line:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        words.append({
                            'word': parts[0].strip(),
                            'pronunciation': f"/{parts[0].strip()}/",
                            'meaning': parts[1].strip()
                        })
    except FileNotFoundError:
        print("警告：单词文件未找到")
    return words

words = load_words()

# 加载用户数据
def load_users():
    if os.path.exists(USERS_DATA_FILE):
        try:
            with open(USERS_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            pass

    # 默认管理员账户
    return {
        'admin': {
            'password': hashlib.md5('admin123'.encode()).hexdigest(),
            'isAdmin': True,
            'progress': {'currentChapter': 0, 'currentGroup': 0, 'masteredWords': []},
            'reviewSchedule': {},
            'stats': {'totalWordsLearned': 0, 'studyDays': 0, 'lastStudyDate': None}
        }
    }

# 保存用户数据
def save_users():
    try:
        with open(USERS_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2, default=str)
    except Exception as e:
        print(f"保存用户数据失败: {e}")

users = load_users()

# 记忆曲线间隔 (小时)
REVIEW_INTERVALS = [1, 24, 72, 168, 336]

# 每章包含的组数和每组包含的单词数
GROUPS_PER_CHAPTER = 3
WORDS_PER_GROUP = 5
TOTAL_WORDS_PER_CHAPTER = GROUPS_PER_CHAPTER * WORDS_PER_GROUP

def hash_password(password):
    return hashlib.md5(password.encode()).hexdigest()

def verify_password(password, hashed):
    return hash_password(password) == hashed

@app.route('/')
def index():
    if 'username' not in session:
        return redirect(url_for('login_page'))

    user = users.get(session['username'])
    if not user:
        session.pop('username', None)
        return redirect(url_for('login_page'))

    # 计算总章数
    total_chapters = (len(words) + TOTAL_WORDS_PER_CHAPTER - 1) // TOTAL_WORDS_PER_CHAPTER

    return render_template('index.html',
                         user=user,
                         username=session['username'],
                         total_chapters=total_chapters,
                         total_words=len(words))

@app.route('/login', methods=['GET', 'POST'])
def login_page():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username in users and verify_password(password, users[username]['password']):
            session['username'] = username
            return redirect(url_for('index'))
        return render_template('login.html', error='用户名或密码错误')
    return render_template('login.html')

@app.route('/api/words')
def get_words():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401

    user = users[session['username']]
    chapter = user['progress']['currentChapter']
    group = user['progress']['currentGroup']

    # 分组逻辑 - 每章3组，每组5个单词
    start = chapter * TOTAL_WORDS_PER_CHAPTER + group * WORDS_PER_GROUP
    end = start + WORDS_PER_GROUP
    current_words = words[start:end] if start < len(words) else []

    # 添加复习状态和索引
    for i, word in enumerate(current_words):
        word['reviewed'] = word['word'] in user['progress']['masteredWords']
        word['index'] = i

    return jsonify({
        'words': current_words,
        'chapter': chapter,
        'group': group,
        'totalChapters': (len(words) + TOTAL_WORDS_PER_CHAPTER - 1) // TOTAL_WORDS_PER_CHAPTER,
        'hasNext': end < len(words)
    })

@app.route('/api/chapter/<int:chapter_num>')
def get_chapter_words(chapter_num):
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401

    user = users[session['username']]

    # 获取整章的单词（3组，每组5个）
    start = chapter_num * TOTAL_WORDS_PER_CHAPTER
    end = start + TOTAL_WORDS_PER_CHAPTER
    chapter_words = words[start:end] if start < len(words) else []

    # 按组分组
    groups = []
    for group_idx in range(GROUPS_PER_CHAPTER):
        group_start = group_idx * WORDS_PER_GROUP
        group_end = group_start + WORDS_PER_GROUP
        group_words = chapter_words[group_start:group_end]

        # 添加状态信息
        for word in group_words:
            word['reviewed'] = word['word'] in user['progress']['masteredWords']

        if group_words:  # 只添加非空组
            groups.append({
                'groupIndex': group_idx,
                'words': group_words
            })

    return jsonify({
        'chapter': chapter_num,
        'groups': groups,
        'totalWords': len(chapter_words)
    })

@app.route('/api/progress', methods=['POST'])
def update_progress():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401

    data = request.json
    username = session['username']

    # 更新学习进度
    if 'currentChapter' in data and 'currentGroup' in data:
        users[username]['progress'].update({
            'currentChapter': data['currentChapter'],
            'currentGroup': data['currentGroup']
        })

    # 标记已掌握单词
    if 'masteredWords' in data:
        old_count = len(users[username]['progress']['masteredWords'])
        users[username]['progress']['masteredWords'] = list(set(
            users[username]['progress']['masteredWords'] + data['masteredWords']
        ))
        new_count = len(users[username]['progress']['masteredWords'])

        # 更新统计信息
        if 'stats' not in users[username]:
            users[username]['stats'] = {'totalWordsLearned': 0, 'studyDays': 0, 'lastStudyDate': None}

        users[username]['stats']['totalWordsLearned'] = new_count
        today = datetime.now().strftime('%Y-%m-%d')
        if users[username]['stats']['lastStudyDate'] != today:
            users[username]['stats']['studyDays'] += 1
            users[username]['stats']['lastStudyDate'] = today

        # 设置复习计划
        for word in data['masteredWords']:
            users[username]['reviewSchedule'][word] = [
                datetime.now() + timedelta(hours=interval)
                for interval in REVIEW_INTERVALS
            ]

    # 保存数据
    save_users()
    return jsonify({'status': 'success'})

@app.route('/api/review')
def get_review_words():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401
    
    username = session['username']
    now = datetime.now()
    review_words = []
    
    # 获取需要复习的单词
    for word, schedule in users[username]['reviewSchedule'].items():
        for i, time in enumerate(schedule):
            if time <= now and i == 0:
                review_words.append({
                    'word': word,
                    'stage': i,
                    'nextReview': schedule[i+1] if i+1 < len(schedule) else None
                })
                break
    
    return jsonify({'words': review_words})

@app.route('/logout')
def logout():
    session.pop('username', None)
    return redirect(url_for('login_page'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            return render_template('register.html', error='用户名和密码不能为空')

        if username in users:
            return render_template('register.html', error='用户名已存在')

        users[username] = {
            'password': hash_password(password),
            'isAdmin': False,
            'progress': {'currentChapter': 0, 'currentGroup': 0, 'masteredWords': []},
            'reviewSchedule': {},
            'stats': {'totalWordsLearned': 0, 'studyDays': 0, 'lastStudyDate': None}
        }
        save_users()
        return redirect(url_for('login_page'))
    return render_template('register.html')

# 新增API：获取学生进度（管理员功能）
@app.route('/api/admin/students')
def get_students():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401

    if not users[session['username']].get('isAdmin'):
        return jsonify({'error': '权限不足'}), 403

    students = []
    for username, user_data in users.items():
        if not user_data.get('isAdmin', False):
            progress = user_data['progress']
            stats = user_data.get('stats', {})
            students.append({
                'username': username,
                'currentChapter': progress['currentChapter'],
                'currentGroup': progress['currentGroup'],
                'masteredWordsCount': len(progress['masteredWords']),
                'totalWordsLearned': stats.get('totalWordsLearned', 0),
                'studyDays': stats.get('studyDays', 0),
                'lastStudyDate': stats.get('lastStudyDate', '从未学习')
            })

    return jsonify({'students': students})

# 新增API：获取九宫格数据
@app.route('/api/nine-grid')
def get_nine_grid():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401

    user = users[session['username']]
    chapter = user['progress']['currentChapter']

    # 获取当前章节的所有单词（3组，每组5个，共15个单词）
    start = chapter * TOTAL_WORDS_PER_CHAPTER
    end = start + TOTAL_WORDS_PER_CHAPTER
    chapter_words = words[start:end] if start < len(words) else []

    # 创建九宫格数据（3x3网格，每个格子包含一组5个单词）
    grid = []
    for group_idx in range(GROUPS_PER_CHAPTER):
        group_start = group_idx * WORDS_PER_GROUP
        group_end = group_start + WORDS_PER_GROUP
        group_words = chapter_words[group_start:group_end]

        # 计算该组的掌握情况
        mastered_count = sum(1 for word in group_words
                           if word['word'] in user['progress']['masteredWords'])

        grid.append({
            'groupIndex': group_idx,
            'words': group_words,
            'masteredCount': mastered_count,
            'totalCount': len(group_words),
            'isCompleted': mastered_count == len(group_words)
        })

    return jsonify({
        'chapter': chapter,
        'grid': grid,
        'totalChapters': (len(words) + TOTAL_WORDS_PER_CHAPTER - 1) // TOTAL_WORDS_PER_CHAPTER
    })

if __name__ == '__main__':
    app.run(debug=True)