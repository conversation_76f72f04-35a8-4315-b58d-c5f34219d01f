from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import json
from datetime import datetime, timedelta

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'

# 加载单词数据
with open('小学考纲单词.md', 'r', encoding='utf-8') as f:
    lines = f.readlines()
    words = []
    for line in lines[2:]:  # 跳过前两行标题
        if line.strip() and '\t' in line:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                words.append({
                    'word': parts[0].strip(),
                    'pronunciation': f"/{parts[0].strip()}/",  # 简单的音标格式
                    'meaning': parts[1].strip()
                })

# 用户数据存储
users = {
    'admin': {
        'password': 'admin123',
        'isAdmin': True,
        'progress': {'currentChapter': 0, 'currentGroup': 0, 'masteredWords': []},
        'reviewSchedule': {}
    }
}

# 记忆曲线间隔 (小时)
REVIEW_INTERVALS = [1, 24, 72, 168, 336]

@app.route('/')
def index():
    if 'username' not in session:
        return redirect(url_for('login_page'))
    return render_template('index.html', user=users.get(session['username']))

@app.route('/login', methods=['GET', 'POST'])
def login_page():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in users and users[username]['password'] == password:
            session['username'] = username
            return redirect(url_for('index'))
        return render_template('login.html', error='用户名或密码错误')
    return render_template('login.html')

@app.route('/api/words')
def get_words():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401
    
    user = users[session['username']]
    chapter = user['progress']['currentChapter']
    group = user['progress']['currentGroup']
    
    # 分组逻辑 - 每组5个单词
    start = (chapter * 9 + group * 3) * 5
    end = start + 5
    current_words = words[start:end]
    
    # 添加复习状态
    for word in current_words:
        word['reviewed'] = word['word'] in user['progress']['masteredWords']
    
    return jsonify({'words': current_words})

@app.route('/api/progress', methods=['POST'])
def update_progress():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401
    
    data = request.json
    username = session['username']
    
    # 更新学习进度
    if 'currentChapter' in data and 'currentGroup' in data:
        users[username]['progress'].update({
            'currentChapter': data['currentChapter'],
            'currentGroup': data['currentGroup']
        })
    
    # 标记已掌握单词
    if 'masteredWords' in data:
        users[username]['progress']['masteredWords'] = list(set(
            users[username]['progress']['masteredWords'] + data['masteredWords']
        ))
        
        # 设置复习计划
        for word in data['masteredWords']:
            users[username]['reviewSchedule'][word] = [
                datetime.now() + timedelta(hours=interval) 
                for interval in REVIEW_INTERVALS
            ]
    
    return jsonify({'status': 'success'})

@app.route('/api/review')
def get_review_words():
    if 'username' not in session:
        return jsonify({'error': '未登录'}), 401
    
    username = session['username']
    now = datetime.now()
    review_words = []
    
    # 获取需要复习的单词
    for word, schedule in users[username]['reviewSchedule'].items():
        for i, time in enumerate(schedule):
            if time <= now and i == 0:
                review_words.append({
                    'word': word,
                    'stage': i,
                    'nextReview': schedule[i+1] if i+1 < len(schedule) else None
                })
                break
    
    return jsonify({'words': review_words})

@app.route('/logout')
def logout():
    session.pop('username', None)
    return redirect(url_for('login_page'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in users:
            return render_template('register.html', error='用户名已存在')
        
        users[username] = {
            'password': password,
            'isAdmin': False,
            'progress': {'currentChapter': 0, 'currentGroup': 0, 'masteredWords': []},
            'reviewSchedule': {}
        }
        return redirect(url_for('login_page'))
    return render_template('register.html')

if __name__ == '__main__':
    app.run(debug=True)